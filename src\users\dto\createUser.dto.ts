import { <PERSON><PERSON><PERSON>, <PERSON>NotEmpty, <PERSON><PERSON><PERSON>, <PERSON>,  } from 'class-validator';
export class CreateUserDto{
    @IsNotEmpty()
    @IsString()
    firstName:string;

    @IsString()
    @IsNotEmpty()
    lastName:string;

    @IsNotEmpty()
    @IsString()
    @IsEmail()
    email:string;

    @IsNotEmpty()
    @IsString()
    @Min(6)
    password:string;

    @IsNotEmpty()
    @IsString()
    mobileNumber:string;

    @IsNotEmpty()
    @IsString()
    gender:string;

    @IsNotEmpty()
    dob:Date;
}