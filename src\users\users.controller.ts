import { Body, Controller, Post } from "@nestjs/common";
import { UsersService } from "./users.service";
import { CreateUserDto } from "./dto/createUser.dto";
import { IUsers } from "./interfaces/users.interface";

@Controller('users')
export class UsersController{
    constructor(private readonly usersService:UsersService){}
    @Post()
    async createUser(@Body() createUserDto:CreateUserDto):Promise<Omit<IUsers,"password">>{
        return this.usersService.createUser(createUserDto);
    }
}