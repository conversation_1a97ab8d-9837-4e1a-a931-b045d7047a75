import { Body, Controller, Post } from "@nestjs/common";
import { AuthService } from "./auth.service";
import { signUpDto } from "./dto/signUp.dto";
import { IAuth } from "./interfaces/auth.interfaces";
import { CreateUserDto } from "src/users/dto/createUser.dto";

@Controller('auth')
export class AuthController{
    constructor(private readonly authService:AuthService){}

    @Post("signup")
    async signUp(@Body() signUpDto:CreateUserDto):Promise<IAuth>{
        console.log("signUpDto",signUpDto);
        return this.authService.signUp(signUpDto);
    }
}