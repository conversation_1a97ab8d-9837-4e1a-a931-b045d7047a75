import { JwtService } from "@nestjs/jwt";
import { UsersService } from "src/users/users.service";
import { signUpDto } from "./dto/signUp.dto";
import { IAuth } from "./interfaces/auth.interfaces";
import { IUsers } from "src/users/interfaces/users.interface";
import { CreateUserDto } from "src/users/dto/createUser.dto";

export class AuthService{
    constructor(private readonly usersService:UsersService,
        private readonly jwtService:JwtService){}

        async signUp(signUpDto:CreateUserDto):Promise<IAuth>{
            console.log(signUpDto);
            const user: Omit<IUsers,"password"> = await this.usersService.createUser(signUpDto);
            const payload={name:user.firstName,_id:user._id};
            const accessToken=await this.jwtService.signAsync(payload);
            return {
                ...user,
                accessToken
            };
        }
}